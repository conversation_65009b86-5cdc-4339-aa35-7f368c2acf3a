import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import datetime
from PIL import Image, ImageTk
import requests
import threading
import time
import json
from urllib.request import urlopen
from io import BytesIO
import model  # 导入model.py模块
import ntplib  # 添加ntplib用于网络时间同步
import uuid
from datetime import timedelta
import concurrent.futures
import os
import configparser


# 配置管理器类
class ConfigManager:
    def __init__(self, config_file="config.ini"):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.load_config()

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                self.config.read(self.config_file, encoding="utf-8")
                print(f"配置文件已加载: {self.config_file}")
            else:
                # 创建默认配置
                self.create_default_config()
                print(f"创建默认配置文件: {self.config_file}")
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self.create_default_config()

    def create_default_config(self):
        """创建默认配置"""
        # 验证码配置
        self.config["captcha"] = {
            "username": "",
            "password": "",
            "quota": "0",
            "verified": "False",
        }

        # 时间同步配置
        self.config["time_sync"] = {
            "sync_interval": "300",  # 5分钟
            "ntp_servers": "ntp.aliyun.com,ntp.tencent.com,cn.pool.ntp.org,time.windows.com,time.apple.com",
        }

        # UI配置
        self.config["ui"] = {
            "update_batch_delay": "50",  # 50ms
            "log_max_lines": "200",
            "log_keep_lines": "100",
        }

        # 任务配置
        self.config["task"] = {
            "default_interval": "1",
            "default_captcha_advance": "3",
            "default_captcha_count": "5",
            "default_retry_interval": "0.5",
        }

        # 窗口配置
        self.config["window"] = {"width": "900", "height": "700", "center": "True"}

        self.save_config()

    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, "w", encoding="utf-8") as f:
                self.config.write(f)
            print(f"配置已保存到: {self.config_file}")
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def get(self, section, key, fallback=None):
        """获取配置值"""
        try:
            return self.config.get(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return fallback

    def getint(self, section, key, fallback=0):
        """获取整数配置值"""
        try:
            return self.config.getint(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback

    def getfloat(self, section, key, fallback=0.0):
        """获取浮点数配置值"""
        try:
            return self.config.getfloat(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback

    def getboolean(self, section, key, fallback=False):
        """获取布尔配置值"""
        try:
            return self.config.getboolean(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback

    def set(self, section, key, value):
        """设置配置值"""
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, key, str(value))

    def update_captcha_config(self, username, password, quota, verified):
        """更新验证码配置"""
        self.set("captcha", "username", username)
        self.set("captcha", "password", password)
        self.set("captcha", "quota", quota)
        self.set("captcha", "verified", verified)
        self.save_config()

    def update_task_defaults(
        self, interval, captcha_advance, captcha_count, retry_interval
    ):
        """更新任务默认配置"""
        self.set("task", "default_interval", interval)
        self.set("task", "default_captcha_advance", captcha_advance)
        self.set("task", "default_captcha_count", captcha_count)
        self.set("task", "default_retry_interval", retry_interval)
        self.save_config()


# 任务状态枚举
class TaskStatus:
    WAITING = "等待中"
    RUNNING = "执行中"
    SUCCESS = "成功"
    FAILED = "失败"
    STOPPED = "已停止"


# 任务类
class AppointmentTask:
    def __init__(self, task_id, config):
        self.task_id = task_id
        self.config = config  # 预约配置信息
        self.status = TaskStatus.WAITING
        self.failure_reason = None  # 失败原因
        self.create_time = datetime.datetime.now()
        self.execute_time = None
        self.result = None
        self.thread = None
        self.is_running = False
        self.log_messages = []
        self.last_sync_time = None  # 上次时间同步时间

    def add_log(self, message):
        """添加任务日志 - 使用优化的时间管理器"""
        # 使用全局时间管理器获取精确时间，性能更好
        try:
            current_time = global_data.time_manager.get_network_time()
            timestamp = current_time.strftime("%H:%M:%S.%f")[:-3]  # 精确到毫秒
        except Exception:
            # 如果时间管理器失败，使用本地时间
            timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]

        log_entry = f"[{timestamp}] {message}"

        # 从配置文件获取日志限制设置
        max_lines = global_data.config_manager.getint("ui", "log_max_lines", 200)
        keep_lines = global_data.config_manager.getint("ui", "log_keep_lines", 100)

        # 限制日志数量，避免内存无限增长
        if len(self.log_messages) >= max_lines:
            self.log_messages = self.log_messages[-keep_lines:]  # 保留最近的日志

        self.log_messages.append(log_entry)

    def set_failed(self, reason):
        """设置任务失败状态和原因"""
        self.status = TaskStatus.FAILED
        self.failure_reason = reason

    def get_display_status(self):
        """获取显示用的状态"""
        if self.status == TaskStatus.FAILED and self.failure_reason:
            return f"{self.status}({self.failure_reason})"
        return self.status


# 时间管理器类 - 优化网络时间同步
class TimeManager:
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.time_offset = 0  # 网络时间与本地时间的偏差（秒）
        self.last_sync_time = None  # 上次同步时间
        self.sync_lock = threading.Lock()

        # 从配置文件加载设置
        self.load_config()

    def load_config(self):
        """从配置文件加载时间同步设置"""
        self.sync_interval = self.config_manager.getint(
            "time_sync", "sync_interval", 300
        )
        ntp_servers_str = self.config_manager.get(
            "time_sync",
            "ntp_servers",
            "ntp.aliyun.com,ntp.tencent.com,cn.pool.ntp.org,time.windows.com,time.apple.com",
        )
        self.ntp_servers = [server.strip() for server in ntp_servers_str.split(",")]

    def get_network_time(self, force_sync=False):
        """获取网络时间，使用缓存的偏移量优化性能"""
        current_local = datetime.datetime.now()

        # 检查是否需要同步
        need_sync = (
            force_sync
            or self.last_sync_time is None
            or (current_local - self.last_sync_time).total_seconds()
            > self.sync_interval
        )

        if need_sync:
            with self.sync_lock:
                # 双重检查，避免重复同步
                if (
                    self.last_sync_time is None
                    or (current_local - self.last_sync_time).total_seconds()
                    > self.sync_interval
                ):
                    self._sync_time_offset()

        # 返回校正后的时间
        return current_local + timedelta(seconds=self.time_offset)

    def _sync_time_offset(self):
        """同步时间偏移量"""
        client = ntplib.NTPClient()

        for server in self.ntp_servers:
            try:
                response = client.request(server, timeout=3)
                network_time = datetime.datetime.fromtimestamp(response.tx_time)
                local_time = datetime.datetime.now()

                # 计算偏移量
                self.time_offset = (network_time - local_time).total_seconds()
                self.last_sync_time = local_time
                return
            except Exception:
                continue

        # 如果所有服务器都失败，保持当前偏移量不变


# UI更新管理器类 - 优化UI更新性能
class UIUpdateManager:
    def __init__(self, root, config_manager):
        self.root = root
        self.config_manager = config_manager
        self.pending_updates = []
        self.update_timer = None
        self.update_lock = threading.Lock()

        # 从配置文件加载设置
        self.batch_delay = self.config_manager.getint("ui", "update_batch_delay", 50)

    def schedule_update(self, update_func):
        """调度UI更新，批量执行以提升性能"""
        with self.update_lock:
            self.pending_updates.append(update_func)
            if not self.update_timer:
                self.update_timer = self.root.after(
                    self.batch_delay, self._batch_update
                )

    def _batch_update(self):
        """批量执行待处理的UI更新"""
        with self.update_lock:
            updates_to_process = self.pending_updates.copy()
            self.pending_updates.clear()
            self.update_timer = None

        # 执行所有待处理的更新
        for update_func in updates_to_process:
            try:
                update_func()
            except Exception as e:
                print(f"UI更新错误: {e}")


# 全局数据存储类
class GlobalData:
    def __init__(self):
        # 初始化配置管理器
        self.config_manager = ConfigManager()

        # 从配置文件加载验证码相关设置
        self.captcha_username = self.config_manager.get("captcha", "username", "")
        self.captcha_password = self.config_manager.get("captcha", "password", "")
        self.captcha_quota = self.config_manager.getfloat("captcha", "quota", 0)
        self.captcha_verified = self.config_manager.getboolean(
            "captcha", "verified", False
        )

        # 账号登录相关
        self.token = None
        self.unionid = None
        self.team_name = None
        self.is_logged_in = False

        # 任务管理
        self.tasks = {}  # 存储所有任务

        # 验证码缓存
        self.captcha_cache = []  # 存储预先获取的验证码
        self.captcha_lock = threading.Lock()  # 验证码缓存锁
        self.captcha_fetch_thread = None  # 验证码获取线程
        self.is_fetching_captcha = False  # 是否正在获取验证码

        # 时间管理器
        self.time_manager = TimeManager(self.config_manager)

    def save_captcha_config(self):
        """保存验证码配置到文件"""
        self.config_manager.update_captcha_config(
            self.captcha_username,
            self.captcha_password,
            self.captcha_quota,
            self.captcha_verified,
        )


# 创建全局数据实例
global_data = GlobalData()


# 预约配置弹窗
class AppointmentConfigDialog:
    def __init__(self, parent):
        self.parent = parent
        self.result = None
        self.config = {}

        # 创建弹窗
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("预约信息配置")
        self.dialog.geometry("700x500")
        self.dialog.resizable(False, False)
        self.dialog.grab_set()  # 模态窗口

        # 居中显示弹窗
        self._center_window(self.dialog, 700, 500)

        # 居中显示
        self.dialog.transient(parent)

        self.create_widgets()

    def _center_window(self, window, width, height):
        """将窗口居中显示"""
        # 获取屏幕尺寸
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()

        # 计算居中位置
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        # 设置窗口位置
        window.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 基本信息区域
        info_frame = ttk.LabelFrame(main_frame, text="基本信息", padding=10)
        info_frame.pack(fill=tk.X, pady=5)

        # 第一行：日期和场次
        row1 = ttk.Frame(info_frame)
        row1.pack(fill=tk.X, pady=5)

        ttk.Label(row1, text="日期:", width=10, anchor=tk.E).grid(
            row=0, column=0, sticky=tk.W, padx=5
        )
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        self.date_entry = ttk.Entry(row1, width=12)
        self.date_entry.insert(0, current_date)
        self.date_entry.grid(row=0, column=1, sticky=tk.W, padx=5)

        self.confirm_date_button = ttk.Button(
            row1, text="确认日期", command=self.update_time_slots
        )
        self.confirm_date_button.grid(row=0, column=2, sticky=tk.W, padx=5)

        ttk.Label(row1, text="场次:", width=8, anchor=tk.E).grid(
            row=0, column=3, sticky=tk.W, padx=5
        )
        self.timeslot_combo = ttk.Combobox(row1, width=25, state="readonly")
        self.timeslot_combo.grid(row=0, column=4, sticky=tk.W, padx=5)

        # 第二行：导游信息
        row2 = ttk.Frame(info_frame)
        row2.pack(fill=tk.X, pady=5)

        ttk.Label(row2, text="导游姓名:", width=10, anchor=tk.E).grid(
            row=0, column=0, sticky=tk.W, padx=5
        )
        self.guide_name_entry = ttk.Entry(row2, width=12)
        self.guide_name_entry.grid(row=0, column=1, sticky=tk.W, padx=5)

        ttk.Label(row2, text="身份证号:", width=8, anchor=tk.E).grid(
            row=0, column=2, sticky=tk.W, padx=5
        )
        self.id_card_entry = ttk.Entry(row2, width=20)
        self.id_card_entry.grid(row=0, column=3, sticky=tk.W, padx=5)

        # 第三行：联系电话
        row3 = ttk.Frame(info_frame)
        row3.pack(fill=tk.X, pady=5)

        ttk.Label(row3, text="联系电话:", width=10, anchor=tk.E).grid(
            row=0, column=0, sticky=tk.W, padx=5
        )
        self.phone_entry = ttk.Entry(row3, width=12)
        self.phone_entry.grid(row=0, column=1, sticky=tk.W, padx=5)

        # 文件上传区域
        file_frame = ttk.LabelFrame(main_frame, text="文件上传", padding=10)
        file_frame.pack(fill=tk.X, pady=5)

        # 上传按钮和状态
        upload_grid = ttk.Frame(file_frame)
        upload_grid.pack(fill=tk.X)

        # 第一行
        self.visitor_list_button = ttk.Button(
            upload_grid, text="上传参观名单", command=self.upload_visitor_list
        )
        self.visitor_list_button.grid(row=0, column=0, padx=5, pady=2, sticky=tk.W)
        self.visitor_list_status = ttk.Label(
            upload_grid, text="未上传", foreground="red"
        )
        self.visitor_list_status.grid(row=0, column=1, padx=5, pady=2, sticky=tk.W)

        self.itinerary_button = ttk.Button(
            upload_grid, text="上传行程单", command=self.upload_itinerary
        )
        self.itinerary_button.grid(row=0, column=2, padx=5, pady=2, sticky=tk.W)
        self.itinerary_status = ttk.Label(upload_grid, text="未上传", foreground="red")
        self.itinerary_status.grid(row=0, column=3, padx=5, pady=2, sticky=tk.W)

        # 第二行
        self.guide_cert_button = ttk.Button(
            upload_grid, text="上传导游证", command=self.upload_guide_cert
        )
        self.guide_cert_button.grid(row=1, column=0, padx=5, pady=2, sticky=tk.W)
        self.guide_cert_status = ttk.Label(upload_grid, text="未上传", foreground="red")
        self.guide_cert_status.grid(row=1, column=1, padx=5, pady=2, sticky=tk.W)

        self.explanation_button = ttk.Button(
            upload_grid, text="上传讲解词", command=self.upload_explanation
        )
        self.explanation_button.grid(row=1, column=2, padx=5, pady=2, sticky=tk.W)
        self.explanation_status = ttk.Label(
            upload_grid, text="未上传", foreground="red"
        )
        self.explanation_status.grid(row=1, column=3, padx=5, pady=2, sticky=tk.W)

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)

        ttk.Button(button_frame, text="确定", command=self.confirm).pack(
            side=tk.RIGHT, padx=5
        )
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(
            side=tk.RIGHT, padx=5
        )

        # 初始化文件上传数据
        self.file_data = {}
        self.time_slot_map = {}

    def update_time_slots(self):
        """获取并更新场次下拉框"""
        if not global_data.is_logged_in or not global_data.token:
            messagebox.showwarning("提示", "请先登录后再获取场次信息")
            return

        date_str = self.date_entry.get().strip()
        if not date_str:
            messagebox.showwarning("提示", "请输入有效的日期")
            return

        self.confirm_date_button.config(state=tk.DISABLED)
        threading.Thread(
            target=self._get_time_slots_thread, args=(date_str,), daemon=True
        ).start()

    def _get_time_slots_thread(self, play_day):
        try:
            time_slots = model.get_time_slots_limit(global_data.token, play_day)

            if time_slots and len(time_slots) > 0:
                slot_options = []
                slot_map = {}

                for slot in time_slots:
                    slot_id = slot["id"]
                    start_time = slot["startTime"]
                    end_time = slot["endTime"]
                    surplus_stock = slot.get("surplusStock", 0)

                    display_text = f"{start_time}-{end_time} (余票: {surplus_stock})"
                    slot_options.append(display_text)
                    slot_map[display_text] = {
                        "id": slot_id,
                        "startTime": start_time,
                        "endTime": end_time,
                        "surplusStock": surplus_stock,
                    }

                self.dialog.after(
                    0, lambda: self._update_time_slot_combo(slot_options, slot_map)
                )
            else:
                self.dialog.after(
                    0, lambda: messagebox.showinfo("提示", "当前日期没有可用场次")
                )
                self.dialog.after(
                    0, lambda: self.confirm_date_button.config(state=tk.NORMAL)
                )
        except Exception as e:
            error_msg = str(e)
            self.dialog.after(
                0,
                lambda: messagebox.showerror("错误", f"获取场次信息失败: {error_msg}"),
            )
            self.dialog.after(
                0, lambda: self.confirm_date_button.config(state=tk.NORMAL)
            )

    def _update_time_slot_combo(self, options, slot_map):
        self.time_slot_map = slot_map
        self.timeslot_combo["values"] = options
        if options:
            self.timeslot_combo.current(0)
        self.confirm_date_button.config(state=tk.NORMAL)

    def upload_visitor_list(self):
        self._upload_file(
            self.visitor_list_button,
            self.visitor_list_status,
            "visitorInformationFileUrl",
            "参观名单",
            model.upload_user_info,
        )

    def upload_itinerary(self):
        self._upload_file(
            self.itinerary_button,
            self.itinerary_status,
            "application_file_url",
            "行程单",
            model.upload_file,
        )

    def upload_guide_cert(self):
        self._upload_file(
            self.guide_cert_button,
            self.guide_cert_status,
            "instructions_prove_fileUrl",
            "导游证",
            model.upload_file,
        )

    def upload_explanation(self):
        self._upload_file(
            self.explanation_button,
            self.explanation_status,
            "explanationFileUrl",
            "讲解词",
            model.upload_file,
        )

    def _upload_file(self, button, status_label, field_name, file_type, upload_func):
        if not global_data.is_logged_in or not global_data.token:
            messagebox.showwarning("提示", "请先登录后再上传文件")
            return

        file_path = filedialog.askopenfilename(
            title=f"选择{file_type}文件", filetypes=[("All Files", "*.*")]
        )

        if not file_path:
            return

        button.config(state=tk.DISABLED)
        threading.Thread(
            target=self._upload_file_thread,
            args=(file_path, button, status_label, field_name, file_type, upload_func),
            daemon=True,
        ).start()

    def _upload_file_thread(
        self, file_path, button, status_label, field_name, file_type, upload_func
    ):
        try:
            filename, res_path = upload_func(file_path, global_data.token)

            if filename and res_path:
                self.file_data[field_name] = res_path
                self.file_data[f"{field_name}_filename"] = filename

                self.dialog.after(
                    0,
                    lambda: self._update_upload_status(
                        status_label,
                        button,
                        "已上传",
                        f"{file_type}上传成功：{filename}",
                    ),
                )
            else:
                self.dialog.after(
                    0,
                    lambda: self._update_upload_failed(button, f"{file_type}上传失败"),
                )
        except Exception as e:
            error_msg = str(e)
            self.dialog.after(
                0,
                lambda: self._update_upload_failed(
                    button, f"{file_type}上传出错：{error_msg}"
                ),
            )

    def _update_upload_status(self, status_label, button, status_text, log_message):
        status_label.config(text=status_text, foreground="green")
        button.config(state=tk.NORMAL)
        messagebox.showinfo("上传成功", log_message)

    def _update_upload_failed(self, button, error_message):
        button.config(state=tk.NORMAL)
        messagebox.showerror("上传失败", error_message)

    def confirm(self):
        """确认配置"""
        # 验证输入
        if not self._validate_config():
            return

        # 收集配置信息
        selected_time_slot = self.timeslot_combo.get()
        time_slot_data = self.time_slot_map[selected_time_slot]

        self.config = {
            "date": self.date_entry.get().strip(),
            "time_slot_id": time_slot_data["id"],
            "time_slot_display": selected_time_slot,
            "guide_name": self.guide_name_entry.get().strip(),
            "id_card": self.id_card_entry.get().strip(),
            "phone": self.phone_entry.get().strip(),
            "files": self.file_data.copy(),
        }

        self.result = True
        self.dialog.destroy()

    def cancel(self):
        """取消配置"""
        self.result = False
        self.dialog.destroy()

    def _validate_config(self):
        """验证配置完整性"""
        if not global_data.is_logged_in:
            messagebox.showwarning("警告", "请先登录账号！")
            return False

        if not self.guide_name_entry.get().strip():
            messagebox.showwarning("警告", "请填写导游姓名！")
            return False

        if not self.id_card_entry.get().strip():
            messagebox.showwarning("警告", "请填写身份证号！")
            return False

        if not self.phone_entry.get().strip():
            messagebox.showwarning("警告", "请填写联系电话！")
            return False

        if not self.timeslot_combo.get():
            messagebox.showwarning("警告", "请先选择预约场次！")
            return False

        # 检查文件上传
        required_files = [
            ("visitorInformationFileUrl", "参观名单"),
            ("application_file_url", "行程单"),
            ("instructions_prove_fileUrl", "导游证"),
            ("explanationFileUrl", "讲解词"),
        ]

        for field, name in required_files:
            if field not in self.file_data:
                messagebox.showwarning("警告", f"请上传{name}！")
                return False

        return True


class AppointmentApp:
    def __init__(self, root):
        self.root = root
        self.root.title("团队预约系统")

        # 从配置文件加载窗口设置
        width = global_data.config_manager.getint("window", "width", 900)
        height = global_data.config_manager.getint("window", "height", 700)
        self.root.geometry(f"{width}x{height}")
        self.root.resizable(False, False)

        # 居中显示主窗口
        if global_data.config_manager.getboolean("window", "center", True):
            self._center_window(self.root, width, height)

        # 账号登录相关变量
        self.qrcode_url = None
        self.qrcode_uuid = None
        self.wx_code_check_thread = None
        self.wx_code_running = False

        # 初始化UI更新管理器
        self.ui_manager = UIUpdateManager(root, global_data.config_manager)

        # 创建主框架
        self.main_frame = ttk.Frame(root, padding=5)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 绑定窗口关闭事件，保存配置
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 创建顶部框架
        self.create_top_frame()

        # 创建任务管理区域
        self.create_task_frame()

        # 创建日志区域
        self.create_log_frame()

        # 加载保存的配置到UI
        self.load_ui_config()

    def _center_window(self, window, width, height):
        """将窗口居中显示"""
        # 获取屏幕尺寸
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()

        # 计算居中位置
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        # 设置窗口位置
        window.geometry(f"{width}x{height}+{x}+{y}")

    def on_closing(self):
        """窗口关闭时保存配置"""
        try:
            # 保存当前的任务配置
            self.save_current_task_config()

            # 保存验证码配置
            global_data.save_captcha_config()

            print("配置已保存")
        except Exception as e:
            print(f"保存配置时出错: {e}")
        finally:
            self.root.destroy()

    def load_ui_config(self):
        """加载UI配置到界面"""
        try:
            # 加载验证码配置
            if global_data.captcha_username:
                self.username_entry.insert(0, global_data.captcha_username)
            if global_data.captcha_password:
                self.password_entry.insert(0, global_data.captcha_password)
            if global_data.captcha_quota > 0:
                self.quota_label.config(
                    text=str(global_data.captcha_quota), foreground="blue"
                )

            # 加载任务默认配置
            default_interval = global_data.config_manager.get(
                "task", "default_interval", "1"
            )
            self.interval_entry.delete(0, tk.END)
            self.interval_entry.insert(0, default_interval)

            default_captcha_advance = global_data.config_manager.get(
                "task", "default_captcha_advance", "3"
            )
            self.captcha_advance_entry.delete(0, tk.END)
            self.captcha_advance_entry.insert(0, default_captcha_advance)

            default_captcha_count = global_data.config_manager.get(
                "task", "default_captcha_count", "5"
            )
            self.captcha_count_entry.delete(0, tk.END)
            self.captcha_count_entry.insert(0, default_captcha_count)

            default_retry_interval = global_data.config_manager.get(
                "task", "default_retry_interval", "0.5"
            )
            self.retry_interval_entry.delete(0, tk.END)
            self.retry_interval_entry.insert(0, default_retry_interval)

            print("UI配置已加载")
        except Exception as e:
            print(f"加载UI配置时出错: {e}")

    def save_current_task_config(self):
        """保存当前任务配置"""
        try:
            interval = self.interval_entry.get().strip()
            captcha_advance = self.captcha_advance_entry.get().strip()
            captcha_count = self.captcha_count_entry.get().strip()
            retry_interval = self.retry_interval_entry.get().strip()

            global_data.config_manager.update_task_defaults(
                interval, captcha_advance, captcha_count, retry_interval
            )
        except Exception as e:
            print(f"保存任务配置时出错: {e}")

    def create_top_frame(self):
        top_frame = ttk.Frame(self.main_frame)
        top_frame.pack(fill=tk.X, padx=3, pady=3)

        # 验证码配置（左侧）
        verification_frame = ttk.LabelFrame(top_frame, text="验证码配置", padding=5)
        verification_frame.pack(side=tk.LEFT, fill=tk.Y, padx=3, pady=3, expand=False)

        # 用户名行
        username_frame = ttk.Frame(verification_frame)
        username_frame.pack(fill=tk.X, padx=3, pady=3)
        ttk.Label(username_frame, text="用户名:", width=8, anchor=tk.E).pack(
            side=tk.LEFT, padx=3
        )
        self.username_entry = ttk.Entry(username_frame, width=15)
        self.username_entry.pack(side=tk.LEFT, padx=3, fill=tk.X, expand=True)

        # 密码行
        password_frame = ttk.Frame(verification_frame)
        password_frame.pack(fill=tk.X, padx=3, pady=3)
        ttk.Label(password_frame, text="密码:", width=8, anchor=tk.E).pack(
            side=tk.LEFT, padx=3
        )
        self.password_entry = ttk.Entry(password_frame, width=15)
        self.password_entry.pack(side=tk.LEFT, padx=3, fill=tk.X, expand=True)

        # 额度行
        quota_frame = ttk.Frame(verification_frame)
        quota_frame.pack(fill=tk.X, padx=3, pady=3)
        ttk.Label(quota_frame, text="额度:", width=8, anchor=tk.E).pack(
            side=tk.LEFT, padx=3
        )
        self.quota_label = ttk.Label(quota_frame, text="0", foreground="blue")
        self.quota_label.pack(side=tk.LEFT, padx=3)

        # 验证按钮
        button_frame = ttk.Frame(verification_frame)
        button_frame.pack(fill=tk.X, padx=3, pady=3)
        self.verify_button = ttk.Button(
            button_frame, text="验证", command=self.verify_captcha_account
        )
        self.verify_button.pack(side=tk.RIGHT, padx=3)

        # 账号登录（右侧）
        login_frame = ttk.LabelFrame(top_frame, text="账号登录", padding=5)
        login_frame.pack(side=tk.RIGHT, fill=tk.BOTH, padx=3, pady=3, expand=True)

        # 旅行社名称
        agency_frame = ttk.Frame(login_frame)
        agency_frame.pack(fill=tk.X, padx=3, pady=3)
        ttk.Label(agency_frame, text="旅行社:").pack(side=tk.LEFT, padx=3)
        self.agency_label = ttk.Label(agency_frame, text="未登录", foreground="red")
        self.agency_label.pack(side=tk.LEFT, padx=3)

        login_content = ttk.Frame(login_frame)
        login_content.pack(fill=tk.BOTH, expand=True)

        # 左侧二维码显示区域
        qr_container = ttk.Frame(login_content)
        qr_container.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        self.qr_frame = ttk.Frame(
            qr_container, width=140, height=140, borderwidth=1, relief="solid"
        )
        self.qr_frame.pack(pady=5, anchor=tk.CENTER)
        self.qr_frame.pack_propagate(False)

        self.qr_label = ttk.Label(self.qr_frame, text="二维码显示区域")
        self.qr_label.pack(expand=True)

        # 右侧按钮区域
        button_container = ttk.Frame(login_content)
        button_container.pack(side=tk.RIGHT, padx=5, pady=5)

        self.refresh_qr_button = ttk.Button(
            button_container, text="刷新二维码", command=self.refresh_qrcode
        )
        self.refresh_qr_button.pack(fill=tk.X, padx=3, pady=3)

        self.login_button = ttk.Button(
            button_container, text="登录", command=self.login_with_qrcode
        )
        self.login_button.pack(fill=tk.X, padx=3, pady=3)

    def create_task_frame(self):
        """创建任务管理区域"""
        task_frame = ttk.LabelFrame(self.main_frame, text="任务管理", padding=5)
        task_frame.pack(fill=tk.BOTH, expand=True, padx=3, pady=3)

        # 全局定时设置区域
        timer_frame = ttk.Frame(task_frame)
        timer_frame.pack(fill=tk.X, pady=5)

        # 第一行：执行时间
        timer_row1 = ttk.Frame(timer_frame)
        timer_row1.pack(fill=tk.X, pady=2)

        ttk.Label(timer_row1, text="全局执行时间:", width=12, anchor=tk.E).pack(
            side=tk.LEFT, padx=5
        )
        current_datetime = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.global_timer_entry = ttk.Entry(timer_row1, width=18)
        self.global_timer_entry.insert(0, current_datetime)
        self.global_timer_entry.pack(side=tk.LEFT, padx=5)

        # 第二行：启动间隔和验证码设置
        timer_row2 = ttk.Frame(timer_frame)
        timer_row2.pack(fill=tk.X, pady=2)

        ttk.Label(timer_row2, text="启动间隔(秒):", width=12, anchor=tk.E).pack(
            side=tk.LEFT, padx=5
        )
        self.interval_entry = ttk.Entry(timer_row2, width=8)
        self.interval_entry.insert(0, "1")
        self.interval_entry.pack(side=tk.LEFT, padx=5)

        ttk.Label(timer_row2, text="验证码提前(秒):", width=12, anchor=tk.E).pack(
            side=tk.LEFT, padx=(20, 5)
        )
        self.captcha_advance_entry = ttk.Entry(timer_row2, width=8)
        self.captcha_advance_entry.insert(0, "3")
        self.captcha_advance_entry.pack(side=tk.LEFT, padx=5)

        ttk.Label(timer_row2, text="验证码数量:", width=10, anchor=tk.E).pack(
            side=tk.LEFT, padx=(20, 5)
        )
        self.captcha_count_entry = ttk.Entry(timer_row2, width=8)
        self.captcha_count_entry.insert(0, "5")
        self.captcha_count_entry.pack(side=tk.LEFT, padx=5)

        ttk.Label(timer_row2, text="重试间隔(秒):", width=10, anchor=tk.E).pack(
            side=tk.LEFT, padx=(20, 5)
        )
        self.retry_interval_entry = ttk.Entry(timer_row2, width=8)
        self.retry_interval_entry.insert(0, "0.5")
        self.retry_interval_entry.pack(side=tk.LEFT, padx=5)

        # 第三行：控制按钮
        timer_row3 = ttk.Frame(timer_frame)
        timer_row3.pack(fill=tk.X, pady=2)

        ttk.Button(timer_row3, text="开始所有任务", command=self.start_all_tasks).pack(
            side=tk.LEFT, padx=5
        )
        ttk.Button(timer_row3, text="停止所有任务", command=self.stop_all_tasks).pack(
            side=tk.LEFT, padx=5
        )
        ttk.Button(timer_row3, text="测试验证码", command=self.test_captcha).pack(
            side=tk.LEFT, padx=5
        )

        # 按钮区域
        button_frame = ttk.Frame(task_frame)
        button_frame.pack(fill=tk.X, pady=5)

        ttk.Button(button_frame, text="创建新任务", command=self.create_new_task).pack(
            side=tk.LEFT, padx=5
        )
        ttk.Button(button_frame, text="停止任务", command=self.stop_selected_task).pack(
            side=tk.LEFT, padx=5
        )
        ttk.Button(
            button_frame, text="删除任务", command=self.delete_selected_task
        ).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="查看详情", command=self.view_task_detail).pack(
            side=tk.LEFT, padx=5
        )
        ttk.Button(button_frame, text="刷新", command=self.refresh_task_list).pack(
            side=tk.RIGHT, padx=5
        )

        # 任务列表
        columns = ("任务ID", "导游姓名", "预约日期", "场次", "状态", "创建时间")
        self.task_tree = ttk.Treeview(
            task_frame, columns=columns, show="headings", height=8
        )

        # 设置列标题和宽度，允许拖拽
        column_widths = {
            "任务ID": 80,
            "导游姓名": 100,
            "预约日期": 90,
            "场次": 150,
            "状态": 80,
            "创建时间": 130,
        }

        for col in columns:
            self.task_tree.heading(col, text=col)
            self.task_tree.column(
                col, width=column_widths.get(col, 100), anchor=tk.CENTER
            )

        # 滚动条
        task_scrollbar = ttk.Scrollbar(
            task_frame, orient="vertical", command=self.task_tree.yview
        )
        self.task_tree.configure(yscrollcommand=task_scrollbar.set)

        self.task_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        task_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_log_frame(self):
        log_frame = ttk.LabelFrame(self.main_frame, text="日志", padding=5)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=3, pady=3)

        self.log_text = tk.Text(log_frame, height=6, width=70, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(
            log_frame, orient="vertical", command=self.log_text.yview
        )
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.log_text.config(state=tk.DISABLED)
        self.add_log("系统已启动")

    def add_log(self, message):
        """添加日志 - 使用优化的时间管理器和UI更新管理器"""
        # 使用优化的时间管理器获取时间
        try:
            current_time = global_data.time_manager.get_network_time()
            time_str = current_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]  # 精确到毫秒
        except Exception:
            # 如果时间管理器失败，使用本地时间
            time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        log_entry = f"[{time_str}] {message}\n"

        # 使用UI更新管理器批量更新，提升性能
        def update_log():
            self.log_text.config(state=tk.NORMAL)
            self.log_text.insert(tk.END, log_entry)

            lines = int(self.log_text.index("end-1c").split(".")[0])
            max_lines = global_data.config_manager.getint("ui", "log_max_lines", 200)
            if lines > max_lines:
                # 删除多余的行，保持在限制范围内
                lines_to_delete = lines - max_lines + 10  # 多删除10行，避免频繁删除
                for _ in range(lines_to_delete):
                    self.log_text.delete("1.0", "2.0")

            self.log_text.see(tk.END)
            self.log_text.config(state=tk.DISABLED)

        self.ui_manager.schedule_update(update_log)

    def create_new_task(self):
        """创建新任务"""
        if not global_data.captcha_verified:
            messagebox.showwarning("警告", "请先验证验证码账号！")
            return

        if not global_data.is_logged_in:
            messagebox.showwarning("警告", "请先登录账号！")
            return

        # 打开配置弹窗
        dialog = AppointmentConfigDialog(self.root)
        self.root.wait_window(dialog.dialog)

        if dialog.result:
            # 创建新任务
            task_id = str(uuid.uuid4())[:8]  # 生成短ID
            task = AppointmentTask(task_id, dialog.config)
            global_data.tasks[task_id] = task

            self.add_log(f"任务 {task_id} 已创建")
            self.refresh_task_list()

    def start_all_tasks(self):
        """开始所有任务"""
        if not global_data.tasks:
            messagebox.showwarning("提示", "没有可执行的任务")
            return

        # 检查验证码账号
        if not global_data.captcha_verified:
            messagebox.showwarning("警告", "请先验证验证码账号！")
            return

        # 检查登录状态
        if not global_data.is_logged_in:
            messagebox.showwarning("警告", "请先登录账号！")
            return

        # 验证时间格式
        try:
            target_time_str = self.global_timer_entry.get().strip()
            target_time = datetime.datetime.strptime(
                target_time_str, "%Y-%m-%d %H:%M:%S"
            )
        except ValueError:
            messagebox.showwarning(
                "警告", "请输入正确的时间格式！(YYYY-MM-DD HH:MM:SS)"
            )
            return

        # 验证间隔时间格式
        try:
            interval = float(self.interval_entry.get().strip())
            if interval < 0:
                messagebox.showwarning("警告", "启动间隔不能为负数！")
                return
        except ValueError:
            messagebox.showwarning("警告", "请输入正确的启动间隔时间！")
            return

        # 验证验证码提前时间
        try:
            advance_seconds = int(self.captcha_advance_entry.get().strip())
            if advance_seconds < 0:
                messagebox.showwarning("警告", "验证码提前时间不能为负数！")
                return
        except ValueError:
            messagebox.showwarning("警告", "请输入正确的验证码提前时间！")
            return

        # 验证验证码数量
        try:
            captcha_count = int(self.captcha_count_entry.get().strip())
            if captcha_count <= 0:
                messagebox.showwarning("警告", "验证码数量必须大于0！")
                return
        except ValueError:
            messagebox.showwarning("警告", "请输入正确的验证码数量！")
            return

        # 获取所有可启动的任务
        tasks_to_start = []
        for _, task in global_data.tasks.items():
            if not task.is_running and task.status in [
                TaskStatus.WAITING,
                TaskStatus.FAILED,
                TaskStatus.STOPPED,
            ]:
                tasks_to_start.append(task)

        if not tasks_to_start:
            messagebox.showinfo("提示", "没有可启动的任务")
            return

        # 清空之前的验证码缓存
        with global_data.captcha_lock:
            global_data.captcha_cache.clear()

        # 启动验证码提前获取定时器
        if advance_seconds > 0:
            self._start_captcha_fetch_timer(target_time, advance_seconds, captcha_count)

        # 启动任务间隔线程
        threading.Thread(
            target=self._start_tasks_with_interval,
            args=(tasks_to_start, target_time, interval),
            daemon=True,
        ).start()

        self.add_log(f"准备启动 {len(tasks_to_start)} 个任务，间隔 {interval} 秒")
        self.add_log(f"目标时间: {target_time_str}")
        if advance_seconds > 0:
            fetch_time = target_time - timedelta(seconds=advance_seconds)
            self.add_log(
                f"验证码获取时间: {fetch_time.strftime('%Y-%m-%d %H:%M:%S')} (提前 {advance_seconds} 秒)"
            )
            self.add_log(f"将获取 {captcha_count} 个验证码")

    def _start_tasks_with_interval(self, tasks, target_time, interval):
        """串行间隔启动任务 - 第一个任务执行完毕后，等待间隔时间再启动下一个"""
        try:
            self.root.after(
                0,
                lambda: self.add_log(
                    f"开始串行间隔启动 {len(tasks)} 个任务，间隔 {interval} 秒"
                ),
            )

            started_count = 0

            for i, task in enumerate(tasks):
                # 检查任务是否仍然可以启动
                if not task.is_running and task.status in [
                    TaskStatus.WAITING,
                    TaskStatus.FAILED,
                    TaskStatus.STOPPED,
                ]:
                    if i == 0:
                        # 第一个任务使用原始目标时间
                        task_target_time = target_time
                        self.root.after(
                            0,
                            lambda idx=i + 1,
                            total=len(tasks),
                            tid=task.task_id: self.add_log(
                                f"启动第 {idx}/{total} 个任务 (ID: {tid}), 目标时间: {target_time.strftime('%Y-%m-%d %H:%M:%S')}"
                            ),
                        )
                    else:
                        # 等待上一个任务完成
                        prev_task = tasks[i - 1]
                        self.root.after(
                            0,
                            lambda idx=i + 1, prev_id=prev_task.task_id: self.add_log(
                                f"等待第 {idx - 1} 个任务 (ID: {prev_id}) 完成..."
                            ),
                        )

                        # 等待上一个任务完成
                        while prev_task.is_running:
                            time.sleep(0.5)  # 每0.5秒检查一次

                        # 检查上一个任务的最终状态
                        if prev_task.status == TaskStatus.STOPPED:
                            self.root.after(
                                0,
                                lambda prev_id=prev_task.task_id: self.add_log(
                                    f"任务 {prev_id} 已停止，跳过后续任务"
                                ),
                            )
                            break
                        elif prev_task.status == TaskStatus.FAILED:
                            self.root.after(
                                0,
                                lambda prev_id=prev_task.task_id: self.add_log(
                                    f"任务 {prev_id} 执行失败，继续执行后续任务"
                                ),
                            )
                        elif prev_task.status == TaskStatus.SUCCESS:
                            self.root.after(
                                0,
                                lambda prev_id=prev_task.task_id: self.add_log(
                                    f"任务 {prev_id} 执行成功"
                                ),
                            )
                        else:
                            self.root.after(
                                0,
                                lambda prev_id=prev_task.task_id,
                                status=prev_task.status: self.add_log(
                                    f"任务 {prev_id} 状态: {status}"
                                ),
                            )

                        # 上一个任务完成，等待间隔时间
                        self.root.after(
                            0,
                            lambda idx=i + 1,
                            total=len(tasks),
                            tid=task.task_id,
                            intv=interval: self.add_log(
                                f"第 {idx - 1} 个任务已完成，等待 {intv} 秒后启动第 {idx}/{total} 个任务 (ID: {tid})"
                            ),
                        )

                        # 等待间隔时间
                        start_wait = datetime.datetime.now()
                        interval_target = start_wait + timedelta(seconds=interval)

                        while datetime.datetime.now() < interval_target:
                            # 检查是否被停止
                            if (
                                not task.is_running
                                and task.status == TaskStatus.STOPPED
                            ):
                                self.root.after(
                                    0,
                                    lambda tid=task.task_id: self.add_log(
                                        f"任务 {tid} 在等待间隔时被停止"
                                    ),
                                )
                                break

                            remaining = (
                                interval_target - datetime.datetime.now()
                            ).total_seconds()
                            if remaining > 5:
                                # 剩余时间较长，记录进度
                                self.root.after(
                                    0,
                                    lambda r=int(remaining): self.add_log(
                                        f"间隔等待中，剩余 {r} 秒"
                                    ),
                                )
                                time.sleep(min(5, remaining))
                            else:
                                time.sleep(0.1)

                        if not task.is_running and task.status == TaskStatus.STOPPED:
                            continue

                        # 计算当前任务的目标执行时间（立即执行）
                        task_target_time = datetime.datetime.now()
                        self.root.after(
                            0,
                            lambda idx=i + 1,
                            total=len(tasks),
                            tid=task.task_id: self.add_log(
                                f"间隔等待完成，立即启动第 {idx}/{total} 个任务 (ID: {tid})"
                            ),
                        )

                    # 启动任务
                    task.thread = threading.Thread(
                        target=self._execute_task,
                        args=(task, task_target_time),
                        daemon=True,
                    )
                    task.is_running = True
                    task.status = TaskStatus.WAITING
                    task.thread.start()
                    started_count += 1

                    # 在任务日志中记录启动信息
                    task.add_log(f"任务已启动 (第 {i + 1}/{len(tasks)} 个)")

                    # 使用UI更新管理器优化界面更新
                    self.ui_manager.schedule_update(self.refresh_task_list)
                else:
                    self.root.after(
                        0,
                        lambda idx=i + 1, tid=task.task_id: self.add_log(
                            f"跳过第 {idx} 个任务 (ID: {tid}) - 状态不符合启动条件"
                        ),
                    )

            # 最终统计
            self.root.after(
                0,
                lambda count=started_count: self.add_log(
                    f"任务串行启动完成，共启动 {count} 个任务"
                ),
            )

        except Exception as e:
            self.root.after(
                0, lambda error=str(e): self.add_log(f"任务启动过程出错: {error}")
            )

    def stop_all_tasks(self):
        """停止所有任务"""
        stopped_count = 0
        for _, task in global_data.tasks.items():
            if task.is_running:
                task.is_running = False
                task.status = TaskStatus.STOPPED
                task.add_log("任务已被停止")
                stopped_count += 1

        if stopped_count > 0:
            self.add_log(f"已停止 {stopped_count} 个任务")
            self.refresh_task_list()
        else:
            messagebox.showinfo("提示", "没有正在运行的任务")

    def _execute_task(self, task, target_time):
        """执行任务"""
        try:
            task.status = TaskStatus.WAITING
            self.ui_manager.schedule_update(self.refresh_task_list)

            # 使用传入的目标时间
            task.add_log(
                f"任务开始等待执行，目标时间: {target_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}"
            )

            # 在主日志中也记录任务开始
            self.root.after(
                0, lambda: self.add_log(f"任务 {task.task_id} 开始等待执行")
            )

            # 使用优化的时间管理器进行时间同步
            network_time = global_data.time_manager.get_network_time(force_sync=True)
            local_time = datetime.datetime.now()
            time_offset = (network_time - local_time).total_seconds()

            task.add_log(
                f"时间同步完成，偏差: {time_offset:.3f}秒，后续使用本地时间计算"
            )

            # 计算基于本地时间的目标时间
            adjusted_target = target_time - timedelta(seconds=time_offset)

            # 等待到达执行时间 - 使用本地时间精确等待
            if task.is_running:
                task.add_log(
                    f"开始等待到目标时间: {adjusted_target.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]} (本地时间)"
                )
                remaining = self.precise_wait_local(adjusted_target, task, "任务执行: ")
                if remaining <= 0:
                    task.add_log("精确等待完成，开始执行任务")
                else:
                    task.add_log(f"精确等待异常，返回值: {remaining}")
                    # 如果精确等待异常，仍然继续执行
                    task.add_log("忽略等待异常，继续执行任务")

            if not task.is_running:
                task.status = TaskStatus.STOPPED
                task.add_log("任务已停止")
                self.ui_manager.schedule_update(
                    lambda: self.add_log(f"任务 {task.task_id} 已停止")
                )
                self.ui_manager.schedule_update(self.refresh_task_list)
                return

            # 开始执行预约 - 基于本地时间计算延迟
            final_local_time = datetime.datetime.now()
            final_network_time = final_local_time + timedelta(
                seconds=time_offset
            )  # 使用之前计算的时间偏差
            actual_delay = (final_network_time - target_time).total_seconds()

            task.status = TaskStatus.RUNNING
            task.execute_time = final_network_time
            target_time_str = target_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

            if abs(actual_delay) < 0.01:  # 10毫秒内为精确
                task.add_log(
                    f"开始执行预约 (目标: {target_time_str}, 延迟: {actual_delay:.3f}秒) ✓超精确"
                )
            elif abs(actual_delay) < 0.1:  # 100毫秒内为精确
                task.add_log(
                    f"开始执行预约 (目标: {target_time_str}, 延迟: {actual_delay:.3f}秒) ✓精确"
                )
            elif abs(actual_delay) < 1.0:  # 1秒内为可接受
                task.add_log(
                    f"开始执行预约 (目标: {target_time_str}, 延迟: {actual_delay:.3f}秒) ⚠️轻微偏差"
                )
            else:
                task.add_log(
                    f"开始执行预约 (目标: {target_time_str}, 延迟: {actual_delay:.3f}秒) ❌时间偏差过大"
                )

            self.root.after(
                0,
                lambda: self.add_log(
                    f"任务 {task.task_id} 开始执行预约，时间偏差: {actual_delay:.3f}秒"
                ),
            )
            self.root.after(0, self.refresh_task_list)

            # 验证码验证
            task.add_log("开始验证码验证...")

            # 检查验证码账号状态
            if not global_data.captcha_verified:
                task.set_failed("验证码账号未验证")
                task.add_log("验证码账号未验证，请先验证验证码账号")
                task.result = {"error": "验证码账号未验证"}
                task.is_running = False
                self.root.after(0, self.refresh_task_list)
                return

            # 优先从缓存中获取验证码
            code_value = self._get_captcha_from_cache()
            if code_value:
                task.add_log("使用缓存验证码")
                with global_data.captcha_lock:
                    cache_count = len(global_data.captcha_cache)
                task.add_log(f"验证码获取成功，剩余缓存: {cache_count}")
            else:
                # 缓存为空，实时获取
                task.add_log("缓存为空，开始实时获取验证码...")
                task.add_log(f"使用验证码账号: {global_data.captcha_username}")
                code_value = model.validate_captcha(
                    global_data.captcha_username,
                    global_data.captcha_password,
                    global_data.token,
                )
                if code_value:
                    task.add_log("实时验证码获取成功")
                else:
                    task.add_log("实时验证码获取失败")
                    task.add_log(
                        f"验证码账号状态 - 用户名: {global_data.captcha_username}, 已验证: {global_data.captcha_verified}"
                    )

            if not code_value:
                task.set_failed("验证码验证失败")
                task.add_log("验证码验证失败")
                task.result = {"error": "验证码验证失败"}
                # 验证码失败时也要设置 is_running = False
                task.is_running = False
                self.ui_manager.schedule_update(self.refresh_task_list)
                return

            task.add_log("验证码验证成功，准备提交预约")

            # 创建签名数据
            signer = model.SignAndEncrypt(
                contact=task.config["guide_name"],
                contactNumber=task.config["phone"],
                application_file_url=task.config["files"]["application_file_url"],
                instructions_prove_fileUrl=task.config["files"][
                    "instructions_prove_fileUrl"
                ],
                explanationFileUrl=task.config["files"]["explanationFileUrl"],
                planDay=task.config["date"],
                timeSlotId=task.config["time_slot_id"],
                visitorInformationFileUrl=task.config["files"][
                    "visitorInformationFileUrl"
                ],
                customerIdCard=task.config["id_card"],
                codeValue=code_value,
            )

            result = signer.generate_signature_and_encrypted_data()

            # 提交预约（包含重试逻辑）
            appointment_result = self._submit_appointment_with_retry(task, result)

            # 保存完整结果
            task.result = appointment_result

            # 输出完整响应日志
            task.add_log(
                f"完整预约响应: {json.dumps(appointment_result, ensure_ascii=False, indent=2)}"
            )

            # 判断成功与否 - 修改判断逻辑
            if appointment_result.get("msg") == "操作成功":
                task.status = TaskStatus.SUCCESS
                success_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[
                    :-3
                ]
                task.add_log(f"[{success_time}] 预约成功！")
                self.root.after(
                    0, lambda: self.add_log(f"任务 {task.task_id} 预约成功！")
                )
            else:
                error_msg = appointment_result.get("msg", "未知错误")
                fail_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[
                    :-3
                ]
                task.set_failed(error_msg)
                task.add_log(f"[{fail_time}] 预约失败: {error_msg}")
                self.root.after(
                    0,
                    lambda msg=error_msg: self.add_log(
                        f"任务 {task.task_id} 预约失败: {msg}"
                    ),
                )

            # 任务执行完毕，设置 is_running = False
            task.is_running = False
            self.ui_manager.schedule_update(self.refresh_task_list)

        except Exception as e:
            error_msg = f"任务执行异常: {str(e)}"
            error_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            task.set_failed(error_msg)
            task.add_log(f"[{error_time}] {error_msg}")
            task.result = {"error": str(e)}
            # 异常时也要设置 is_running = False
            task.is_running = False
            self.root.after(
                0,
                lambda err=error_msg: self.add_log(
                    f"任务 {task.task_id} 执行异常: {err}"
                ),
            )
            self.root.after(0, self.refresh_task_list)

    def _submit_appointment_with_retry(self, task, sign_result):
        """提交预约请求，包含重试逻辑"""
        max_retries = 1  # 最多重试1次
        appointment_result = None  # 初始化结果变量

        for attempt in range(max_retries + 1):
            attempt_num = attempt + 1

            if attempt == 0:
                task.add_log("正在提交预约请求...")
            else:
                task.add_log(f"开始第 {attempt} 次重试...")

                # 获取重试间隔
                try:
                    retry_interval = float(self.retry_interval_entry.get().strip())
                    if retry_interval > 0:
                        task.add_log(f"等待 {retry_interval} 秒后重试...")
                        time.sleep(retry_interval)
                except ValueError:
                    retry_interval = 0.5  # 默认0.5秒
                    task.add_log(f"使用默认重试间隔 {retry_interval} 秒")
                    time.sleep(retry_interval)

                # 重试时获取新的验证码
                task.add_log("获取新验证码用于重试...")
                new_code_value = self._get_captcha_from_cache()
                if new_code_value:
                    task.add_log("使用缓存验证码重试")
                    with global_data.captcha_lock:
                        cache_count = len(global_data.captcha_cache)
                    task.add_log(f"验证码获取成功，剩余缓存: {cache_count}")
                else:
                    # 缓存为空，实时获取
                    task.add_log("缓存为空，实时获取验证码...")
                    new_code_value = model.validate_captcha(
                        global_data.captcha_username,
                        global_data.captcha_password,
                        global_data.token,
                    )
                    if new_code_value:
                        task.add_log("实时验证码获取成功")
                    else:
                        task.add_log("实时验证码获取失败")

                if not new_code_value:
                    task.add_log("无法获取新验证码，停止重试")
                    # 返回上一次的结果，如果没有则返回错误结果
                    return appointment_result or {
                        "msg": "无法获取验证码",
                        "error": True,
                    }

                # 重新生成签名数据
                task.add_log("重新生成签名数据...")
                signer = model.SignAndEncrypt(
                    contact=task.config["guide_name"],
                    contactNumber=task.config["phone"],
                    application_file_url=task.config["files"]["application_file_url"],
                    instructions_prove_fileUrl=task.config["files"][
                        "instructions_prove_fileUrl"
                    ],
                    explanationFileUrl=task.config["files"]["explanationFileUrl"],
                    planDay=task.config["date"],
                    timeSlotId=task.config["time_slot_id"],
                    visitorInformationFileUrl=task.config["files"][
                        "visitorInformationFileUrl"
                    ],
                    customerIdCard=task.config["id_card"],
                    codeValue=new_code_value,
                )
                sign_result = signer.generate_signature_and_encrypted_data()

            # 提交预约请求
            submit_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            task.add_log(f"[{submit_time}] 发送预约请求 (第 {attempt_num} 次)")

            appointment_result = model.createOrder(
                global_data.token,
                sign_result["signData"],
                sign_result["sign"],
                global_data.unionid,
            )

            # 检查结果
            result_msg = appointment_result.get("msg", "未知错误")
            task.add_log(f"第 {attempt_num} 次请求响应: {result_msg}")

            # 如果成功，直接返回
            if appointment_result.get("msg") == "操作成功":
                if attempt > 0:
                    task.add_log(f"重试成功！第 {attempt_num} 次请求成功")
                return appointment_result

            # 如果是最后一次尝试，返回结果
            if attempt == max_retries:
                task.add_log(f"所有重试已完成，最终结果: {result_msg}")
                return appointment_result

            # 否则准备重试
            task.add_log(f"第 {attempt_num} 次请求失败: {result_msg}，准备重试...")

        return appointment_result

    def stop_selected_task(self):
        """停止选中的任务"""
        selection = self.task_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请选择要停止的任务")
            return

        task_id = self.task_tree.item(selection[0])["values"][0]
        task = global_data.tasks.get(task_id)

        if task and task.is_running:
            task.is_running = False
            task.status = TaskStatus.STOPPED
            task.add_log("任务已被手动停止")
            self.add_log(f"任务 {task_id} 已停止")
            self.refresh_task_list()
        else:
            messagebox.showinfo("提示", "任务未在运行中")

    def delete_selected_task(self):
        """删除选中的任务"""
        selection = self.task_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请选择要删除的任务")
            return

        if messagebox.askyesno("确认", "确定要删除选中的任务吗？"):
            task_id = self.task_tree.item(selection[0])["values"][0]
            task = global_data.tasks.get(task_id)

            if task and task.is_running:
                task.is_running = False

            del global_data.tasks[task_id]
            self.add_log(f"任务 {task_id} 已删除")
            self.refresh_task_list()

    def view_task_detail(self):
        """查看任务详情"""
        selection = self.task_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请选择要查看的任务")
            return

        task_id = self.task_tree.item(selection[0])["values"][0]
        task = global_data.tasks.get(task_id)

        if task:
            self._show_task_detail_dialog(task)

    def _show_task_detail_dialog(self, task):
        """显示任务详情对话框"""
        detail_window = tk.Toplevel(self.root)
        detail_window.title(f"任务详情 - {task.task_id}")
        detail_window.geometry("600x500")
        detail_window.grab_set()

        # 居中显示弹窗
        self._center_window(detail_window, 600, 500)

        # 基本信息
        info_frame = ttk.LabelFrame(detail_window, text="基本信息", padding=10)
        info_frame.pack(fill=tk.X, padx=10, pady=5)

        info_text = f"""任务ID: {task.task_id}
导游姓名: {task.config["guide_name"]}
预约日期: {task.config["date"]}
场次: {task.config["time_slot_display"]}
状态: {task.get_display_status()}
创建时间: {task.create_time.strftime("%Y-%m-%d %H:%M:%S")}"""

        if task.execute_time:
            info_text += (
                f"\n实际执行时间: {task.execute_time.strftime('%Y-%m-%d %H:%M:%S')}"
            )

        ttk.Label(info_frame, text=info_text, justify=tk.LEFT).pack(anchor=tk.W)

        # 任务日志
        log_frame = ttk.LabelFrame(detail_window, text="任务日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        log_text = tk.Text(log_frame, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(
            log_frame, orient="vertical", command=log_text.yview
        )
        log_text.configure(yscrollcommand=log_scrollbar.set)

        # 显示日志
        for log_msg in task.log_messages:
            log_text.insert(tk.END, log_msg + "\n")

        # 如果有结果，显示完整结果
        if task.result:
            log_text.insert(tk.END, "\n=== 完整预约结果 ===\n")
            log_text.insert(
                tk.END, json.dumps(task.result, ensure_ascii=False, indent=2)
            )

        log_text.config(state=tk.DISABLED)
        log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 关闭按钮
        ttk.Button(detail_window, text="关闭", command=detail_window.destroy).pack(
            pady=10
        )

    def refresh_task_list(self):
        """刷新任务列表"""
        # 清空现有数据
        for item in self.task_tree.get_children():
            self.task_tree.delete(item)

        # 添加任务数据
        for _, task in global_data.tasks.items():
            values = (
                task.task_id,
                task.config["guide_name"],
                task.config["date"],
                task.config["time_slot_display"],
                task.get_display_status(),
                task.create_time.strftime("%m-%d %H:%M:%S"),
            )
            self.task_tree.insert("", "end", values=values)

    def precise_wait_local(self, target_time, task=None, log_prefix=""):
        """基于本地时间的精确等待 - 毫秒级精度，无网络延迟"""
        try:
            current_local = datetime.datetime.now()
            time_diff = (target_time - current_local).total_seconds()

            if time_diff <= 0:
                return 0

            if task:
                task.add_log(
                    f"{log_prefix}开始本地时间精确等待，剩余 {time_diff:.3f} 秒"
                )
            else:
                self.root.after(
                    0,
                    lambda td=time_diff: self.add_log(
                        f"{log_prefix}开始本地时间精确等待，剩余 {td:.3f} 秒"
                    ),
                )

            # 纯本地时间等待策略
            while True:
                current_local = datetime.datetime.now()
                remaining = (target_time - current_local).total_seconds()

                if remaining <= 0:
                    break

                if remaining <= 0.02:
                    # 最后20毫秒使用超高精度忙等待
                    if task:
                        task.add_log(
                            f"{log_prefix}进入超高精度忙等待，剩余 {remaining:.3f} 秒"
                        )
                    else:
                        self.root.after(
                            0,
                            lambda r=remaining: self.add_log(
                                f"{log_prefix}进入超高精度忙等待，剩余 {r:.3f} 秒"
                            ),
                        )

                    # 超高精度忙等待循环
                    while True:
                        current_local = datetime.datetime.now()
                        remaining = (target_time - current_local).total_seconds()
                        if remaining <= 0:
                            break

                        # 根据剩余时间调整等待精度
                        if remaining > 0.01:  # 大于10毫秒
                            time.sleep(0.001)  # 1毫秒
                        elif remaining > 0.005:  # 大于5毫秒
                            time.sleep(0.0005)  # 0.5毫秒
                        elif remaining > 0.002:  # 大于2毫秒
                            time.sleep(0.0001)  # 0.1毫秒
                        else:
                            # 小于2毫秒，使用最短的睡眠
                            time.sleep(0.00001)  # 0.01毫秒
                    break
                elif remaining <= 0.1:
                    # 100毫秒内使用高精度sleep
                    sleep_time = max(0.001, remaining * 0.5)  # 睡眠50%的时间，留50%余量
                    time.sleep(sleep_time)
                elif remaining <= 1.0:
                    # 1秒内使用中精度sleep
                    sleep_time = max(0.01, remaining * 0.7)  # 睡眠70%的时间，留30%余量
                    time.sleep(sleep_time)
                elif remaining <= 5:
                    # 5秒内使用常规精度sleep
                    sleep_time = max(0.1, remaining * 0.8)  # 睡眠80%的时间，留20%余量
                    time.sleep(sleep_time)
                elif remaining <= 30:
                    # 30秒内使用长时间sleep
                    sleep_time = max(1.0, remaining * 0.9)  # 睡眠90%的时间，留10%余量
                    time.sleep(sleep_time)
                else:
                    # 超过30秒，使用超长时间sleep
                    sleep_time = max(5.0, remaining * 0.95)  # 睡眠95%的时间，留5%余量
                    time.sleep(sleep_time)

            return 0
        except Exception as e:
            error_msg = f"{log_prefix}本地时间精确等待出错: {str(e)}"
            if task:
                task.add_log(error_msg)
            else:
                self.root.after(0, lambda: self.add_log(error_msg))
            return -1

    def verify_captcha_account(self):
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()

        if not username or not password:
            messagebox.showwarning("警告", "请输入用户名和密码！")
            return

        self.add_log(f"正在验证账号 {username}...")
        self.verify_button.config(state=tk.DISABLED)

        # 创建线程进行验证
        threading.Thread(
            target=self._verify_captcha_thread, args=(username, password), daemon=True
        ).start()

    def _verify_captcha_thread(self, username, password):
        try:
            # 使用提供的余额查询接口验证账号
            url = "http://www.fdyscloud.com.cn/tuling/balance"

            # 准备请求数据
            data = {"username": username, "password": password}

            # 发送请求
            response = requests.post(url, json=data, timeout=10)
            print(response.text)

            # 处理响应
            if response.status_code == 200:
                result = response.json()
                if "result" in result:
                    # 获取余额成功 - 处理 {"result": "83.9"} 格式的响应
                    try:
                        quota = float(result["result"])

                        # 保存到全局数据
                        global global_data
                        global_data.captcha_username = username
                        global_data.captcha_password = password
                        global_data.captcha_quota = quota
                        global_data.captcha_verified = True

                        # 保存到配置文件
                        global_data.save_captcha_config()

                        # 在UI线程中更新UI
                        self.root.after(
                            0,
                            lambda: self._update_captcha_verification_result(
                                True, username, quota
                            ),
                        )
                    except ValueError:
                        # 结果不是有效数字
                        self.root.after(
                            0,
                            lambda: self._update_captcha_verification_result(
                                False, username, "无效的余额数据"
                            ),
                        )
                else:
                    # 响应中没有余额信息
                    error_msg = result.get("msg", "未知错误")
                    self.root.after(
                        0,
                        lambda: self._update_captcha_verification_result(
                            False, username, error_msg
                        ),
                    )
            else:
                # 请求失败
                self.root.after(
                    0,
                    lambda: self._update_captcha_verification_result(
                        False, username, f"请求失败: {response.status_code}"
                    ),
                )
        except Exception as e:
            # 在UI线程中显示错误
            error_msg = str(e)
            self.root.after(
                0,
                lambda: self._update_captcha_verification_result(
                    False, username, error_msg
                ),
            )

    def _update_captcha_verification_result(self, success, username, result):
        self.verify_button.config(state=tk.NORMAL)

        if success:
            self.quota_label.config(text=str(result), foreground="blue")
            messagebox.showinfo(
                "成功", f"验证成功！账号 {username} 的剩余额度: {result}"
            )
            self.add_log(f"验证成功！账号 {username} 的剩余额度: {result}")
        else:
            messagebox.showerror("错误", f"验证失败: {result}")
            self.add_log(f"验证失败: {result}")

    def refresh_qrcode(self):
        self.add_log("正在获取微信登录二维码...")

        # 禁用按钮
        self.refresh_qr_button.config(state=tk.DISABLED)
        self.login_button.config(state=tk.DISABLED)

        # 创建线程获取二维码
        threading.Thread(target=self._get_qrcode_thread, daemon=True).start()

    def _get_qrcode_thread(self):
        try:
            # 调用model中的获取二维码函数
            qrcode_url, qrcode_uuid = model.get_wechat_qrcode_url()

            if qrcode_url and qrcode_uuid:
                # 保存URL和UUID
                self.qrcode_url = qrcode_url
                self.qrcode_uuid = qrcode_uuid

                # 下载并显示二维码
                try:
                    response = urlopen(qrcode_url)
                    qr_data = response.read()
                    qr_image = Image.open(BytesIO(qr_data))
                    qr_image = qr_image.resize((130, 130), Image.LANCZOS)
                    self.qr_photo = ImageTk.PhotoImage(qr_image)

                    # 在UI线程中更新二维码
                    self.root.after(0, self._update_qrcode_image)
                except Exception as e:
                    error_msg = str(e)
                    self.root.after(
                        0, lambda: self.add_log(f"下载二维码失败: {error_msg}")
                    )
            else:
                self.root.after(0, lambda: self.add_log("获取二维码失败，请重试"))

            # 在UI线程中重新启用按钮
            self.root.after(0, lambda: self.refresh_qr_button.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.login_button.config(state=tk.NORMAL))

        except Exception as e:
            # 在UI线程中处理错误
            error_msg = str(e)
            self.root.after(0, lambda: self.add_log(f"获取二维码出错: {error_msg}"))
            self.root.after(0, lambda: self.refresh_qr_button.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.login_button.config(state=tk.NORMAL))

    def _update_qrcode_image(self):
        # 清除现有内容
        for widget in self.qr_frame.winfo_children():
            widget.destroy()

        # 显示二维码
        qr_label = ttk.Label(self.qr_frame, image=self.qr_photo)
        qr_label.pack(expand=True)

        self.add_log("二维码已更新，请使用微信扫描")

    def login_with_qrcode(self):
        if not self.qrcode_uuid:
            messagebox.showwarning("警告", "请先刷新二维码！")
            return

        # 如果已有正在运行的线程，则停止它
        if (
            self.wx_code_running
            and self.wx_code_check_thread
            and self.wx_code_check_thread.is_alive()
        ):
            self.wx_code_running = False
            self.add_log("正在停止上一次登录检测...")
            return

        self.add_log("开始检测微信扫码状态...")
        self.login_button.config(text="停止检测")

        # 启动线程检测登录状态
        self.wx_code_running = True
        self.wx_code_check_thread = threading.Thread(
            target=self._check_wx_login_thread, daemon=True
        )
        self.wx_code_check_thread.start()

    def _check_wx_login_thread(self):
        try:
            # 每隔2秒检查一次登录状态
            while self.wx_code_running:
                wx_code = model.get_wx_code(self.qrcode_uuid)

                if wx_code:
                    # 获取到登录码，进行后续登录流程
                    self.root.after(
                        0, lambda: self.add_log(f"获取到微信认证码: {wx_code}")
                    )

                    # 获取skey
                    skey = model.get_skey(wx_code)
                    if skey:
                        self.root.after(0, lambda: self.add_log("成功获取skey"))

                        # 获取token和unionid
                        token_info = model.get_token(skey)
                        if (
                            token_info
                            and "token" in token_info
                            and "unionid" in token_info
                        ):
                            # 保存到全局数据
                            global global_data
                            global_data.token = token_info["token"]
                            global_data.unionid = token_info["unionid"]

                            # 获取团队名称
                            team_name = model.get_team_name(global_data.token)
                            if team_name:
                                # 保存团队名称到全局数据
                                global_data.team_name = team_name
                                global_data.is_logged_in = True

                                # 登录成功，更新UI
                                self.root.after(
                                    0, lambda: self._update_login_success(team_name)
                                )
                                break
                            else:
                                self.root.after(
                                    0, lambda: self.add_log("获取团队名称失败")
                                )
                        else:
                            self.root.after(0, lambda: self.add_log("获取token失败"))
                    else:
                        self.root.after(0, lambda: self.add_log("获取skey失败"))

                time.sleep(2)

            # 检查完成后，更新按钮状态
            if self.wx_code_running:  # 只有当没有被手动停止时才执行
                self.root.after(0, lambda: self.login_button.config(text="登录"))
                self.wx_code_running = False

        except Exception as e:
            error_msg = str(e)
            self.root.after(0, lambda: self.add_log(f"登录检测出错: {error_msg}"))
            self.root.after(0, lambda: self.login_button.config(text="登录"))
            self.wx_code_running = False

    def _update_login_success(self, team_name):
        # 更新UI显示
        self.agency_label.config(text=team_name, foreground="green")
        self.login_button.config(text="登录")
        self.wx_code_running = False

        # 显示登录成功弹窗
        messagebox.showinfo("登录成功", f"成功登录到团队: {team_name}")
        self.add_log(f"成功登录到团队: {team_name}")

    def test_captcha(self):
        """测试验证码获取"""
        if not global_data.captcha_verified:
            messagebox.showwarning("警告", "请先验证验证码账号！")
            return

        if not global_data.is_logged_in:
            messagebox.showwarning("警告", "请先登录账号！")
            return

        try:
            count = int(self.captcha_count_entry.get().strip())
            if count <= 0:
                messagebox.showwarning("警告", "验证码数量必须大于0！")
                return
        except ValueError:
            messagebox.showwarning("警告", "请输入正确的验证码数量！")
            return

        self.add_log(f"开始测试获取 {count} 个验证码...")
        threading.Thread(
            target=self._test_captcha_thread, args=(count,), daemon=True
        ).start()

    def _test_captcha_thread(self, count):
        """测试验证码获取线程"""
        try:
            success_count = 0
            fail_count = 0

            for i in range(count):
                try:
                    code_value = model.validate_captcha(
                        global_data.captcha_username,
                        global_data.captcha_password,
                        global_data.token,
                    )
                    if code_value:
                        success_count += 1
                        self.root.after(
                            0, lambda idx=i + 1: self.add_log(f"验证码 {idx} 获取成功")
                        )
                    else:
                        fail_count += 1
                        self.root.after(
                            0, lambda idx=i + 1: self.add_log(f"验证码 {idx} 获取失败")
                        )
                except Exception as e:
                    fail_count += 1
                    self.root.after(
                        0,
                        lambda idx=i + 1, err=str(e): self.add_log(
                            f"验证码 {idx} 获取异常: {err}"
                        ),
                    )

            self.root.after(
                0,
                lambda s=success_count, f=fail_count: self.add_log(
                    f"验证码测试完成，成功: {s}，失败: {f}"
                ),
            )

        except Exception as e:
            self.root.after(
                0, lambda err=str(e): self.add_log(f"验证码测试出错: {err}")
            )

    def _fetch_captcha_batch(self, count):
        """并发获取指定数量的验证码"""
        if global_data.is_fetching_captcha:
            self.add_log("验证码获取线程已在运行中")
            return

        global_data.is_fetching_captcha = True
        self.add_log(f"开始并发获取 {count} 个验证码...")

        def fetch_single_captcha(index):
            """获取单个验证码"""
            try:
                code_value = model.validate_captcha(
                    global_data.captcha_username,
                    global_data.captcha_password,
                    global_data.token,
                )
                if code_value:
                    with global_data.captcha_lock:
                        global_data.captcha_cache.append(code_value)
                    self.root.after(
                        0, lambda idx=index: self.add_log(f"验证码 {idx} 获取成功")
                    )
                    return True
                else:
                    self.root.after(
                        0, lambda idx=index: self.add_log(f"验证码 {idx} 获取失败")
                    )
                    return False
            except Exception as e:
                self.root.after(
                    0,
                    lambda idx=index, err=str(e): self.add_log(
                        f"验证码 {idx} 获取异常: {err}"
                    ),
                )
                return False

        # 使用线程池并发获取验证码
        with concurrent.futures.ThreadPoolExecutor(
            max_workers=min(count, 10)
        ) as executor:
            futures = [
                executor.submit(fetch_single_captcha, i + 1) for i in range(count)
            ]
            success_count = sum(
                1
                for future in concurrent.futures.as_completed(futures)
                if future.result()
            )

        global_data.is_fetching_captcha = False
        with global_data.captcha_lock:
            cache_count = len(global_data.captcha_cache)

        self.root.after(
            0,
            lambda s=success_count, c=cache_count: self.add_log(
                f"验证码获取完成，成功: {s}，缓存数量: {c}"
            ),
        )

    def _get_captcha_from_cache(self):
        """从缓存中获取一个验证码"""
        with global_data.captcha_lock:
            if global_data.captcha_cache:
                return global_data.captcha_cache.pop(0)
            else:
                return None

    def _start_captcha_fetch_timer(self, target_time, advance_seconds, captcha_count):
        """启动验证码提前获取定时器"""
        fetch_time = target_time - timedelta(seconds=advance_seconds)

        def wait_and_fetch():
            # 使用优化的时间管理器进行时间同步
            network_time = global_data.time_manager.get_network_time(force_sync=True)
            local_time = datetime.datetime.now()
            time_offset = (network_time - local_time).total_seconds()

            self.ui_manager.schedule_update(
                lambda: self.add_log(
                    f"验证码获取时间同步完成，偏差: {time_offset:.3f}秒"
                )
            )

            # 计算基于本地时间的获取时间
            adjusted_fetch_time = fetch_time - timedelta(seconds=time_offset)

            # 使用本地时间精确等待
            self.root.after(
                0,
                lambda: self.add_log(
                    f"开始等待到验证码获取时间: {adjusted_fetch_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]} (本地时间)"
                ),
            )
            remaining = self.precise_wait_local(
                adjusted_fetch_time, None, "验证码获取: "
            )
            if remaining <= 0:
                self.root.after(0, lambda: self.add_log("精确等待完成，开始获取验证码"))
                self._fetch_captcha_batch(captcha_count)
            else:
                self.root.after(
                    0,
                    lambda r=remaining: self.add_log(
                        f"验证码获取精确等待异常，返回值: {r}"
                    ),
                )
                # 如果精确等待异常，仍然获取验证码
                self.root.after(0, lambda: self.add_log("忽略等待异常，开始获取验证码"))
                self._fetch_captcha_batch(captcha_count)

        # 启动等待线程
        threading.Thread(target=wait_and_fetch, daemon=True).start()


if __name__ == "__main__":
    root = tk.Tk()
    app = AppointmentApp(root)
    root.mainloop()
