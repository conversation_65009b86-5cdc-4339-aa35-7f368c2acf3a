import re
import time
import requests
from bs4 import BeautifulSoup
import json
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Signature import pkcs1_15
from Crypto.Hash import SHA1
from Crypto.PublicKey import RSA
import random
from datetime import datetime, timedelta


class SignAndEncrypt:
    def __init__(
        self,
        contact,
        contactNumber,
        application_file_url,
        instructions_prove_fileUrl,
        explanationFileUrl,
        planDay,
        timeSlotId,
        visitorInformationFileUrl,
        customerIdCard,
        codeValue,
    ):
        self.contact = contact
        self.contactNumber = contactNumber
        self.application_file_url = application_file_url
        self.instructions_prove_fileUrl = instructions_prove_fileUrl
        self.explanationFileUrl = explanationFileUrl
        self.planDay = planDay
        self.timeSlotId = timeSlotId
        self.visitorInformationFileUrl = visitorInformationFileUrl
        self.customerIdCard = customerIdCard
        self.codeValue = codeValue
        self.aes_key = "rw9ptc2y3gszxj3d"  # Given AES key
        self.aes_iv = "dg93rvmqu8nrfu88"  # Given IV
        self.rsa_private_key_pem = """***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""

    def aes_cbc_encrypt(self, data):
        key = self.aes_key.encode("utf-8")
        iv = self.aes_iv.encode("utf-8")

        cipher = AES.new(key, AES.MODE_CBC, iv)
        padded_data = pad(data.encode("utf-8"), AES.block_size, style="pkcs7")
        encrypted = cipher.encrypt(padded_data)
        return base64.b64encode(encrypted).decode("utf-8")

    def autograph(self, data):
        private_key = RSA.import_key(self.rsa_private_key_pem)
        h = SHA1.new(data.encode("utf-8"))
        signature = pkcs1_15.new(private_key).sign(h)
        return base64.b64encode(signature).decode("utf-8")

    def generate_signature_and_encrypted_data(self):
        # 原始 JSON 数据
        original_data = {
            "teamType": 1,
            "merchantNo": "E888888886",
            "isExplanationServiceConsultation": 0,
            "contact": self.contact,
            "contactNumber": self.contactNumber,
            "application_file_url": self.application_file_url,
            "instructions_prove_fileUrl": self.instructions_prove_fileUrl,
            "explanationFileUrl": self.explanationFileUrl,
            "teacherExplanation": 0,
            "teamAppointmentItemParamList": [
                {
                    "planDay": self.planDay,
                    "timeSlotId": self.timeSlotId,
                    "visitorInformationFileUrl": self.visitorInformationFileUrl,
                }
            ],
        }

        # 其他信息
        additional_data = {
            "customerIdCard": self.customerIdCard,
            "customerIdCardType": 0,
            "orderType": 1,
            "sourceType": 2,
            "payType": 0,
            "contactNumber": self.contactNumber,
            "teacherExplanation": 0,
            "captchaInfo": {
                "captchaType": 4,
                "normalCode": {"codeValue": self.codeValue},
            },
        }

        # 将团队数据包装在 data 字段内
        data_str = json.dumps(
            [original_data], separators=(",", ":"), ensure_ascii=False
        )
        # 构造完整的字典结构
        final_data = {
            "data": data_str,
        }
        final_data.update(additional_data)

        # 将 final_data 转换为 JSON 字符串
        final_json_str = json.dumps(
            final_data, separators=(",", ":"), ensure_ascii=False
        )

        # AES加密
        encrypted_data = self.aes_cbc_encrypt(final_json_str)

        # RSA签名
        signature = self.autograph(encrypted_data)

        return {
            "sign": signature,
            "signData": encrypted_data,
        }


def get_wechat_qrcode_url():
    url = "https://open.weixin.qq.com/connect/qrconnect"

    params = {
        "appid": "wx929f9043b03800fd",
        "scope": "snsapi_login",
        "redirect_uri": "https://www.sxd.cn/login",
        "state": "0694341673",
        "login_type": "jssdk",
        "self_redirect": "false",
        "styletype": "",
        "sizetype": "",
        "bgcolor": "",
        "rst": "",
    }

    response = requests.get(url, params=params)

    soup = BeautifulSoup(response.text, "html.parser")

    # 提取src链接
    img_tag = soup.find("img", class_="qrcode lightBorder js_qrcode_img")
    if img_tag:
        src = img_tag["src"]
        full_url = f"https://open.weixin.qq.com{src}"
        qrcode_str = src.split("/")[-1]
        return full_url, qrcode_str
    else:
        return None, None


def get_wx_code(uuid):
    url = "https://lp.open.weixin.qq.com/connect/l/qrconnect"

    timestamp = int(time.time() * 1000)

    params = {"uuid": uuid, "_": str(timestamp)}

    response = requests.get(url, params=params)

    match = re.search(r"window\.wx_code='(.*?)';", response.text)
    if match:
        wx_code = match.group(1)
        return wx_code if wx_code else None
    return None


def get_skey(code):
    url = "https://pulic-auth-api.sxd.cn/usrv-center/api/v1/base/third-login"

    data = {"appId": "wx929f9043b03800fd", "thirdType": "WX_WEB", "code": code}

    response = requests.post(url, json=data)

    if response.status_code == 200:
        response_json = response.json()
        if "data" in response_json and "skey" in response_json["data"]:
            return response_json["data"]["skey"]

    return None


def get_token(skey):
    url = "https://ticket.sxd.cn/api-api/app/clientHelper/pass/loginForgw"

    data = {
        "merchantNo": "E888888886",
        "appId": "f780ea5b76544c1fa20da8048f1dd424",
        "skey": skey,
    }

    response = requests.post(url, json=data)

    if response.status_code == 200:
        response_json = response.json()
        if "token" in response_json and "unionid" in response_json["data"]:
            return {
                "token": response_json["token"],
                "unionid": response_json["data"]["unionid"],
            }

    return None


def get_team_name(authorization):
    url = "https://ticket.sxd.cn/api-api/app/clientHelper/teamManage/checkIsTeam"

    headers = {
        "authorization": authorization,
    }

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        response_json = response.json()
        if "data" in response_json and "teamName" in response_json["data"]:
            return response_json["data"]["teamName"]

    return None


def get_appointment_days(authorization):
    url = "https://ticket.sxd.cn/api-api/app/clientHelper/appointmentDay"

    headers = {
        "Authorization": authorization,
    }

    params = {"type": 1}

    response = requests.get(url, headers=headers, params=params)

    if response.status_code == 200:
        response_json = response.json()
        if "data" in response_json:
            appointment_days = [
                (day["day"], day["week"]) for day in response_json["data"]
            ]
            return appointment_days

    return None


def get_time_slots_limit(authorization, play_day):
    url = "https://ticket.sxd.cn/api-api/app/clientHelper/getTimeSlotsLimit"

    headers = {
        "Authorization": authorization,
    }

    data = {"orderType": 1, "sourceType": 2, "playDay": play_day, "teamType": 1}

    response = requests.post(url, headers=headers, json=data)

    if response.status_code == 200:
        response_json = response.json()
        if "data" in response_json:
            time_slots = [
                {
                    "id": slot["id"],
                    "startTime": slot["startTime"],
                    "endTime": slot["endTime"],
                    "surplusStock": slot.get("surplusStock", 0),
                    "limitNums": slot.get("limitNums", 0),
                }
                for slot in response_json["data"]
            ]
            return time_slots

    return None


def upload_user_info(file_path, authorization):
    url = "https://ticket.sxd.cn/api-api/app/clientHelper/uploadUserInfo"

    headers = {
        "Authorization": authorization,
    }

    files = {"file": (file_path, open(file_path, "rb"), "application/octet-stream")}

    data = {"teamType": 1}

    response = requests.post(url, headers=headers, files=files, data=data)

    if response.status_code == 200:
        response_json = response.json()
        if "originalFilename" in response_json and "resPath" in response_json:
            return response_json["originalFilename"], response_json["resPath"]

    return None, None


def upload_file(file_path, authorization):
    url = "https://ticket.sxd.cn/api-cms/file/upload"

    headers = {
        "Authorization": authorization,
    }

    files = {"file": (file_path, open(file_path, "rb"), "application/octet-stream")}

    response = requests.post(url, headers=headers, files=files)

    if response.status_code == 200:
        response_json = response.json()
        if "originalFilename" in response_json and "resPath" in response_json:
            return response_json["originalFilename"], response_json["resPath"]

    return None, None


def generate_captcha(authorization):
    url = "https://ticket.sxd.cn/api-api/captcha/gen"
    headers = {
        "Authorization": authorization,
    }
    response = requests.post(url, headers=headers, timeout=1.5)  # 设置超时时间为2秒
    if response.status_code == 200:
        return response.json()
    else:
        return None


def recognize_captcha(username, password, template_image, background_image):
    url = "http://fdyscloud.com.cn/tuling/predict"
    data = {
        "username": username,
        "password": password,
        "b64_small": template_image,
        "b64_large": background_image,
        "ID": "47834705",
        "version": "3.1.1",
    }
    response = requests.post(url, json=data, timeout=1.5)  # 设置超时时间为2秒
    if response.status_code == 200:
        return response.json()
    else:
        return None


def generate_random_track(start_x, start_y, end_x, end_y, start_time, num_points=10):
    track = []
    current_time = start_time
    for _ in range(num_points):
        x = random.randint(min(start_x, end_x), max(start_x, end_x))
        y = random.randint(min(start_y, end_y), max(start_y, end_y))
        t = random.randint(100, 500)  # 随机时间间隔，单位：毫秒
        current_time += timedelta(milliseconds=t)
        track.append(
            {
                "x": x,
                "y": y,
                "type": "move",
                "t": int((current_time - start_time).total_seconds() * 1000),
            }
        )
    return track


def get_and_recognize_captcha(username, password, authorization):
    captcha_data = generate_captcha(authorization)
    if captcha_data:
        template_image = captcha_data["captcha"]["templateImage"]
        background_image = captcha_data["captcha"]["backgroundImage"]

        # 去除 base64 数据前缀
        template_image = template_image.split(",")[1]
        background_image = background_image.split(",")[1]

        recognition_result = recognize_captcha(
            username, password, template_image, background_image
        )
        return (
            recognition_result,
            captcha_data["id"],
            captcha_data["captcha"]["backgroundImageWidth"],
            captcha_data["captcha"]["backgroundImageHeight"],
        )
    else:
        return None, None, None, None


def validate_captcha(username, password, authorization):
    while True:
        try:
            recognition_result, captcha_id, bg_width, bg_height = (
                get_and_recognize_captcha(username, password, authorization)
            )
            if recognition_result and len(recognition_result["data"]) >= 4:
                click_coordinates = [
                    recognition_result["data"]["顺序1"],
                    recognition_result["data"]["顺序2"],
                    recognition_result["data"]["顺序3"],
                    recognition_result["data"]["顺序4"],
                ]

                track_list = []
                start_time = datetime.utcnow()

                for i, coord in enumerate(click_coordinates):
                    x = coord["X坐标值"]
                    y = coord["Y坐标值"]
                    if i > 0:
                        prev_x = click_coordinates[i - 1]["X坐标值"]
                        prev_y = click_coordinates[i - 1]["Y坐标值"]
                        track_list.extend(
                            generate_random_track(prev_x, prev_y, x, y, start_time)
                        )
                    current_time = datetime.utcnow()
                    track_list.append(
                        {
                            "x": x,
                            "y": y,
                            "type": "click",
                            "t": int(
                                (current_time - start_time).total_seconds() * 1000
                            ),
                        }
                    )

                # 增加一定的时间间隔
                end_time = (
                    start_time
                    + timedelta(seconds=5)
                    + timedelta(milliseconds=random.randint(0, 1000))
                )

                data = {
                    "id": captcha_id,
                    "data": {
                        "bgImageWidth": bg_width,
                        "bgImageHeight": bg_height,
                        "startSlidingTime": start_time.isoformat() + "Z",
                        "endSlidingTime": end_time.isoformat() + "Z",
                        "trackList": track_list,
                    },
                }

                headers = {
                    "Authorization": authorization,
                }

                url = "https://ticket.sxd.cn/api-api/captcha/check"
                response = requests.post(
                    url, headers=headers, json=data, timeout=1.5
                )  # 设置超时时间为2秒
                result = response.json()
                print(result)

                if result["code"] == 200 and result["success"]:
                    return result["data"]["id"]
                elif result["code"] == 4001 and result["msg"] == "基础校验失败":
                    continue  # 重新走流程
            else:
                continue  # 重新走流程
        except (requests.RequestException, requests.Timeout) as e:
            print(f"网络错误或请求超时：{e}")
            continue  # 重新走流程


def createOrder(authorization, signData, sign, unionId):
    url = "https://ticket.sxd.cn/api-api/app/clientHelper/v1/createOrder"

    headers = {
        "authorization": authorization,
    }

    data = {
        "createTime": int(time.time() * 1000),
        "sign": sign,
        "signData": signData,
        "sourceType": 2,
        "unionId": unionId,
    }

    # 发送 POST 请求
    response = requests.post(url, headers=headers, json=data)
    print(f"预约返回:{response.json()}")
    # 返回响应信息
    return response.json()
